import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const groupSchema = z.object({
  name: z.string().min(1),
  courseId: z.string(),
  teacherId: z.string(),
  capacity: z.number().min(1).max(50),
  schedule: z.string(),
  room: z.string().optional(),
  cabinetId: z.string().optional(),
  branch: z.string(),
  startDate: z.string(),
  endDate: z.string(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const branch = searchParams.get('branch')
    const isActive = searchParams.get('isActive')

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { course: { name: { contains: search, mode: 'insensitive' } } },
        { teacher: { user: { name: { contains: search, mode: 'insensitive' } } } },
      ]
    }

    if (branch) {
      // Map branch ID to branch name for database query
      const branchName = branch === 'main' ? 'Main Branch' : 'Branch'
      where.branch = branchName
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    const [groups, total] = await Promise.all([
      prisma.group.findMany({
        where,
        include: {
          course: {
            select: {
              name: true,
              level: true,
            },
          },
          teacher: {
            select: {
              id: true,
              tier: true,
              subject: true,
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          enrollments: {
            include: {
              student: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              enrollments: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.group.count({ where }),
    ])

    return NextResponse.json({
      groups,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching groups:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = groupSchema.parse(body)

    const group = await prisma.group.create({
      data: {
        ...validatedData,
        startDate: new Date(validatedData.startDate),
        endDate: new Date(validatedData.endDate),
      },
      include: {
        course: {
          select: {
            name: true,
            level: true,
          },
        },
        teacher: {
          select: {
            id: true,
            tier: true,
            subject: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        _count: {
          select: {
            enrollments: true,
          },
        },
      },
    })

    return NextResponse.json(group, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating group:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
