'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { formatDate } from '@/lib/utils'
import { Search, Plus, Users, Calendar, Clock, User, Edit, Trash2, Loader2, BookOpen } from 'lucide-react'
import GroupForm from '@/components/forms/group-form'
import { useBranch } from '@/contexts/branch-context'
import CoursesTab from '@/components/groups/courses-tab'

interface Group {
  id: string
  name: string
  capacity: number
  schedule: string
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  course: {
    name: string
    level: string
  }
  teacher: {
    id: string
    tier: string
    subject: string
    user: {
      name: string
    }
  }
  enrollments: {
    id: string
    student: {
      user: {
        name: string
      }
    }
    status: string
  }[]
  _count: {
    enrollments: number
  }
}

// Helper functions for teacher tier styling
const getTeacherTierStyle = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold'
    case 'B_LEVEL':
      return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium'
    case 'C_LEVEL':
      return 'bg-gradient-to-r from-green-400 to-green-600 text-white'
    case 'NEW':
      return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getTeacherTierLabel = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'A-Level'
    case 'B_LEVEL':
      return 'B-Level'
    case 'C_LEVEL':
      return 'C-Level'
    case 'NEW':
      return 'New'
    default:
      return 'Unknown'
  }
}

// Group Card Component
function GroupCard({
  group,
  onEdit,
  onDelete
}: {
  group: Group
  onEdit: (group: Group) => void
  onDelete: (groupId: string) => void
}) {
  const getLevelColor = (level: string) => {
    const colors: { [key: string]: string } = {
      'A1': 'bg-red-100 text-red-800',
      'A2': 'bg-orange-100 text-orange-800',
      'B1': 'bg-yellow-100 text-yellow-800',
      'B2': 'bg-green-100 text-green-800',
      'IELTS': 'bg-indigo-100 text-indigo-800',
      'SAT': 'bg-cyan-100 text-cyan-800',
      'MATH': 'bg-emerald-100 text-emerald-800',
      'KIDS': 'bg-pink-100 text-pink-800',
    }
    return colors[level] || 'bg-gray-100 text-gray-800'
  }

  const getTimeFromSchedule = (schedule: string) => {
    const timeMatch = schedule.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
    return timeMatch ? `${timeMatch[1]}:${timeMatch[2]} - ${timeMatch[3]}:${timeMatch[4]}` : ''
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header with Level and Student Count */}
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-3">
              <Badge className={getLevelColor(group.course.level)} variant="secondary">
                {group.course.level.replace('_', ' ')}
              </Badge>
              <Badge className="bg-blue-100 text-blue-800" variant="secondary">
                {group._count.enrollments}/{group.capacity} students
              </Badge>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(group)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(group.id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Group Name */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900">{group.name}</h4>
            <p className="text-sm text-gray-600">{group.course.name}</p>
          </div>

          {/* Teacher */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-medium">Teacher: {group.teacher.user.name}</span>
            </div>
            <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle(group.teacher.tier || 'NEW')}`}>
              {getTeacherTierLabel(group.teacher.tier || 'NEW')}
            </span>
          </div>

          {/* Time */}
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-sm">{getTimeFromSchedule(group.schedule)}</span>
          </div>

          {/* Start Date */}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm">Start date: {formatDate(new Date(group.startDate))}</span>
          </div>

          {/* Status Badges */}
          <div className="flex flex-wrap gap-2">
            <Badge className={getStatusColor(group.isActive)} variant="secondary">
              {group.isActive ? 'Started' : 'Not Started'}
            </Badge>
            <Badge className="bg-blue-100 text-blue-800" variant="secondary">
              UZBEK
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default function GroupsPage() {
  const { currentBranch } = useBranch()
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('')
  const [languageFilter, setLanguageFilter] = useState('')
  const [scheduleFilter, setScheduleFilter] = useState('')
  const [teacherTierFilter, setTeacherTierFilter] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (currentBranch?.id) {
      fetchGroups()
    }
  }, [currentBranch?.id])

  const fetchGroups = async () => {
    if (!currentBranch?.id) return

    try {
      setLoading(true)
      const response = await fetch(`/api/groups?branch=${currentBranch.id}`)
      const data = await response.json()
      setGroups(data.groups || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching groups:', error)
      setError('Failed to fetch groups')
    } finally {
      setLoading(false)
    }
  }

  // Handle group creation
  const handleCreateGroup = async (data: any) => {
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create group')
      }

      setIsCreateDialogOpen(false)
      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle group update
  const handleUpdateGroup = async (data: any) => {
    if (!editingGroup) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/groups/${editingGroup.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update group')
      }

      setIsEditDialogOpen(false)
      setEditingGroup(null)
      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle group deletion
  const handleDeleteGroup = async (groupId: string) => {
    if (!confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/groups/${groupId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete group')
      }

      fetchGroups() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  // Filter groups based on all criteria
  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesLevel = !levelFilter || levelFilter === 'all' || group.course.level === levelFilter
    const matchesLanguage = !languageFilter || languageFilter === 'all' || group.course.name.toLowerCase().includes(languageFilter.toLowerCase())
    const matchesTeacherTier = !teacherTierFilter || teacherTierFilter === 'all' || group.teacher.tier === teacherTierFilter

    // Schedule filter based on time period
    let matchesSchedule = true
    if (scheduleFilter) {
      const timePeriod = getTimePeriod(group.schedule).toLowerCase()
      matchesSchedule = timePeriod === scheduleFilter
    }

    return matchesSearch && matchesLevel && matchesLanguage && matchesSchedule && matchesTeacherTier
  }).sort((a, b) => {
    // Sort by teacher tier priority (A_LEVEL first, then B_LEVEL, C_LEVEL, NEW)
    const tierPriority: Record<string, number> = { 'A_LEVEL': 1, 'B_LEVEL': 2, 'C_LEVEL': 3, 'NEW': 4 }
    const aTier = tierPriority[a.teacher.tier || 'NEW'] || 4
    const bTier = tierPriority[b.teacher.tier || 'NEW'] || 4
    return aTier - bTier
  })

  // Separate groups by schedule type
  const mwfGroups = filteredGroups.filter(group =>
    group.schedule.toLowerCase().includes('monday') &&
    group.schedule.toLowerCase().includes('wednesday') &&
    group.schedule.toLowerCase().includes('friday')
  )

  const ttsGroups = filteredGroups.filter(group =>
    group.schedule.toLowerCase().includes('tuesday') &&
    group.schedule.toLowerCase().includes('thursday') &&
    group.schedule.toLowerCase().includes('saturday')
  )

  // Helper function to determine schedule type
  function getScheduleType(schedule: string): string {
    const lowerSchedule = schedule.toLowerCase()
    if (lowerSchedule.includes('monday') && lowerSchedule.includes('wednesday') && lowerSchedule.includes('friday')) {
      return 'mwf'
    }
    if (lowerSchedule.includes('tuesday') && lowerSchedule.includes('thursday') && lowerSchedule.includes('saturday')) {
      return 'tts'
    }
    return 'other'
  }

  // Helper function to get time period from schedule
  function getTimePeriod(schedule: string): string {
    const timeMatch = schedule.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
    if (!timeMatch) return ''

    const startHour = parseInt(timeMatch[1])
    if (startHour < 12) return 'Morning'
    if (startHour < 17) return 'Afternoon'
    return 'Evening'
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading groups...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Groups</h1>
          <p className="text-gray-600">Manage class groups and schedules</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Add New Group
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Group</DialogTitle>
              <DialogDescription>
                Set up a new class group with course, teacher, and schedule details.
              </DialogDescription>
            </DialogHeader>
            <GroupForm
              onSubmit={handleCreateGroup}
              onCancel={() => setIsCreateDialogOpen(false)}
              isEditing={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="groups" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="groups" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Groups
          </TabsTrigger>
          <TabsTrigger value="courses" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            Courses
          </TabsTrigger>
        </TabsList>

        <TabsContent value="groups" className="space-y-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Select Level</label>
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="A1">A1</SelectItem>
                  <SelectItem value="A2">A2</SelectItem>
                  <SelectItem value="B1">B1</SelectItem>
                  <SelectItem value="B2">B2</SelectItem>
                  <SelectItem value="IELTS">IELTS</SelectItem>
                  <SelectItem value="SAT">SAT</SelectItem>
                  <SelectItem value="MATH">MATH</SelectItem>
                  <SelectItem value="KIDS">KIDS</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Language</label>
              <Select value={languageFilter} onValueChange={setLanguageFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Languages" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Languages</SelectItem>
                  <SelectItem value="english">English</SelectItem>
                  <SelectItem value="russian">Russian</SelectItem>
                  <SelectItem value="uzbek">Uzbek</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Teacher Tier</label>
              <Select value={teacherTierFilter} onValueChange={setTeacherTierFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Tiers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="A_LEVEL">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('A_LEVEL')}`}>
                        A-Level
                      </span>
                      <span>A-Level Teachers</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="B_LEVEL">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('B_LEVEL')}`}>
                        B-Level
                      </span>
                      <span>B-Level Teachers</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="C_LEVEL">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('C_LEVEL')}`}>
                        C-Level
                      </span>
                      <span>C-Level Teachers</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="NEW">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle('NEW')}`}>
                        New
                      </span>
                      <span>New Teachers</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="relative">
              <Search className="absolute left-3 top-8 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <label className="text-sm font-medium text-gray-700">Search</label>
              <Input
                placeholder="Search by teacher or group name"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Schedule Filter Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={scheduleFilter === '' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('')}
              className="text-sm"
            >
              Available Groups
            </Button>
            <Button
              variant={scheduleFilter === 'morning' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('morning')}
              className="text-sm"
            >
              Morning Groups
            </Button>
            <Button
              variant={scheduleFilter === 'afternoon' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('afternoon')}
              className="text-sm"
            >
              Afternoon Groups
            </Button>
            <Button
              variant={scheduleFilter === 'evening' ? 'default' : 'outline'}
              onClick={() => setScheduleFilter('evening')}
              className="text-sm"
            >
              Evening Groups
            </Button>
          </div>

          {/* Two-Column Layout for Groups */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Monday/Wednesday/Friday Groups */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-600">Monday/Wednesday/Friday</h3>
              <div className="space-y-4">
                {mwfGroups.map((group) => (
                  <GroupCard
                    key={group.id}
                    group={group}
                    onEdit={(group) => {
                      setEditingGroup(group)
                      setIsEditDialogOpen(true)
                    }}
                    onDelete={handleDeleteGroup}
                  />
                ))}
                {mwfGroups.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No M/W/F groups found
                  </div>
                )}
              </div>
            </div>

            {/* Tuesday/Thursday/Saturday Groups */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-blue-600">Tuesday/Thursday/Saturday</h3>
              <div className="space-y-4">
                {ttsGroups.map((group) => (
                  <GroupCard
                    key={group.id}
                    group={group}
                    onEdit={(group) => {
                      setEditingGroup(group)
                      setIsEditDialogOpen(true)
                    }}
                    onDelete={handleDeleteGroup}
                  />
                ))}
                {ttsGroups.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No T/T/S groups found
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Groups</p>
                <p className="text-2xl font-bold text-gray-900">{groups.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Groups</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.filter(g => g.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.reduce((sum, group) => sum + group._count.enrollments, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg. Group Size</p>
                <p className="text-2xl font-bold text-gray-900">
                  {groups.length > 0 
                    ? Math.round(groups.reduce((sum, group) => sum + group._count.enrollments, 0) / groups.length)
                    : 0
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses" className="space-y-6">
          <CoursesTab />
        </TabsContent>
      </Tabs>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Group</DialogTitle>
            <DialogDescription>
              Update group information, schedule, and settings.
            </DialogDescription>
          </DialogHeader>
          {editingGroup && (
            <GroupForm
              initialData={{
                name: editingGroup.name,
                courseId: '', // You'll need to get this from the group data
                teacherId: '', // You'll need to get this from the group data
                capacity: editingGroup.capacity,
                schedule: editingGroup.schedule,
                room: editingGroup.room || '',
                branch: editingGroup.branch,
                startDate: new Date(editingGroup.startDate).toISOString().split('T')[0],
                endDate: new Date(editingGroup.endDate).toISOString().split('T')[0],
                isActive: editingGroup.isActive
              }}
              onSubmit={handleUpdateGroup}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingGroup(null)
              }}
              isEditing={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
