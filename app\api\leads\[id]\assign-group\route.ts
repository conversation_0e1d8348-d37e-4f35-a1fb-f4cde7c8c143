import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const assignGroupSchema = z.object({
  groupId: z.string().min(1, 'Group ID is required'),
  notes: z.string().optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = assignGroupSchema.parse(body)

    // Check if lead exists and is in correct status
    const lead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    if (lead.status !== 'CALL_COMPLETED') {
      return NextResponse.json(
        { error: 'Lead must be in CALL_COMPLETED status to assign to group' },
        { status: 400 }
      )
    }

    // Check if group exists and has capacity
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        course: { select: { name: true, level: true } },
        teacher: { include: { user: { select: { name: true } } } },
        _count: { select: { enrollments: true } }
      }
    })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    if (!group.isActive) {
      return NextResponse.json({ error: 'Group is not active' }, { status: 400 })
    }

    if (group._count.enrollments >= group.capacity) {
      return NextResponse.json({ error: 'Group is at full capacity' }, { status: 400 })
    }

    const now = new Date()

    // Update lead with group assignment
    const updatedLead = await prisma.lead.update({
      where: { id },
      data: {
        status: 'GROUP_ASSIGNED',
        assignedGroupId: validatedData.groupId,
        assignedTeacherId: group.teacherId,
        assignedAt: now,
        notes: validatedData.notes || lead.notes,
        updatedAt: now,
      },
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        assignedTeacher: {
          include: { user: { select: { name: true } } }
        }
      }
    })

    // Log activity
    await ActivityLogger.logLeadContacted(
      session.user.id,
      session.user.role as Role,
      lead.id,
      {
        leadName: lead.name,
        leadPhone: lead.phone,
        previousStatus: lead.status,
        newStatus: 'GROUP_ASSIGNED',
        notes: `Assigned to group: ${group.name} (${group.course.name} - ${group.course.level}) with teacher: ${group.teacher.user.name}`,
      },
      request
    )

    return NextResponse.json({
      lead: updatedLead,
      group: group,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error assigning group to lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get available groups for assignment
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url)
    const teacherFilter = searchParams.get('teacher')
    const levelFilter = searchParams.get('level')
    const search = searchParams.get('search')
    const branchId = searchParams.get('branch') || 'main'

    // Map branch ID to branch name for database query
    const branchName = branchId === 'main' ? 'Main Branch' : 'Branch'

    const where: any = {
      isActive: true,
      branch: branchName,
    }

    // Filter by teacher
    if (teacherFilter) {
      where.teacher = {
        user: {
          name: { contains: teacherFilter, mode: 'insensitive' }
        }
      }
    }

    // Filter by level
    if (levelFilter) {
      where.course = {
        level: levelFilter
      }
    }

    // Search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { course: { name: { contains: search, mode: 'insensitive' } } },
        { teacher: { user: { name: { contains: search, mode: 'insensitive' } } } }
      ]
    }

    const groups = await prisma.group.findMany({
      where,
      include: {
        course: { select: { name: true, level: true } },
        teacher: {
          select: {
            id: true,
            tier: true,
            subject: true,
            user: {
              select: { name: true }
            },
          },
        },
        _count: { select: { enrollments: true } }
      },
      orderBy: [
        { startDate: 'asc' },
        { name: 'asc' }
      ]
    })

    // Filter out groups at capacity
    const availableGroups = groups.filter(group => group._count.enrollments < group.capacity)

    // Sort by teacher tier priority (A_LEVEL first, then B_LEVEL, C_LEVEL, NEW)
    const tierPriority: Record<string, number> = { 'A_LEVEL': 1, 'B_LEVEL': 2, 'C_LEVEL': 3, 'NEW': 4 }
    const sortedGroups = availableGroups.sort((a, b) => {
      const aTier = tierPriority[a.teacher.tier || 'NEW'] || 4
      const bTier = tierPriority[b.teacher.tier || 'NEW'] || 4
      return aTier - bTier
    })

    return NextResponse.json({ groups: sortedGroups })
  } catch (error) {
    console.error('Error fetching available groups:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
